cmake_minimum_required(VERSION 3.10)

project(RayTracer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add compiler flags for optimization and warnings
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O3")
elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /O2")
endif()

# Create the main executable
add_executable(raytracer main.cpp)

# Create the test scene executable
add_executable(test_scene test_scene.cpp)

# Create the new feature demonstration executables
add_executable(cornell_box cornell_box.cpp)
add_executable(motion_blur motion_blur.cpp)
add_executable(textures textures.cpp)
add_executable(volumes volumes.cpp)
add_executable(triangles triangles.cpp)
add_executable(final_scene final_scene.cpp)

# Set output directory for all executables
set_target_properties(raytracer test_scene cornell_box motion_blur textures volumes triangles final_scene
    PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}
)
