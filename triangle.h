#ifndef TRIANGLE_H
#define TRIANGLE_H

#include "rtweekend.h"
#include "hittable.h"
#include "aabb.h"

class triangle : public hittable {
public:
    triangle(const point3& v0, const point3& v1, const point3& v2, shared_ptr<material> mat)
        : v0(v0), v1(v1), v2(v2), mat(mat) {
        
        // Compute triangle normal
        vec3 edge1 = v1 - v0;
        vec3 edge2 = v2 - v0;
        normal = unit_vector(cross(edge1, edge2));
        
        // Compute bounding box
        point3 min_point(
            std::fmin(std::fmin(v0.x(), v1.x()), v2.x()),
            std::fmin(std::fmin(v0.y(), v1.y()), v2.y()),
            std::fmin(std::fmin(v0.z(), v1.z()), v2.z())
        );
        point3 max_point(
            std::fmax(std::fmax(v0.x(), v1.x()), v2.x()),
            std::fmax(std::fmax(v0.y(), v1.y()), v2.y()),
            std::fmax(std::fmax(v0.z(), v1.z()), v2.z())
        );
        
        // Expand slightly to avoid numerical issues
        bbox = aabb(min_point, max_point).expand(0.0001);
    }

    bool hit(const ray& r, interval ray_t, hit_record& rec) const override {
        // Möller-Trumbore ray-triangle intersection algorithm
        const double EPSILON = 0.0000001;
        
        vec3 edge1 = v1 - v0;
        vec3 edge2 = v2 - v0;
        vec3 h = cross(r.direction(), edge2);
        double a = dot(edge1, h);
        
        if (a > -EPSILON && a < EPSILON)
            return false;    // Ray is parallel to triangle
            
        double f = 1.0 / a;
        vec3 s = r.origin() - v0;
        double u = f * dot(s, h);
        
        if (u < 0.0 || u > 1.0)
            return false;
            
        vec3 q = cross(s, edge1);
        double v = f * dot(r.direction(), q);
        
        if (v < 0.0 || u + v > 1.0)
            return false;
            
        // At this stage we can compute t to find out where the intersection point is on the line
        double t = f * dot(edge2, q);
        
        if (!ray_t.contains(t))
            return false;
            
        // Ray intersection
        rec.t = t;
        rec.p = r.at(t);
        rec.set_face_normal(r, normal);
        rec.u = u;
        rec.v = v;
        rec.mat = mat;
        
        return true;
    }

    aabb bounding_box() const override { return bbox; }

private:
    point3 v0, v1, v2;
    vec3 normal;
    shared_ptr<material> mat;
    aabb bbox;
};

#endif
