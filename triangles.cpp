#include "rtweekend.h"

#include "bvh.h"
#include "camera.h"
#include "color.h"
#include "hittable_list.h"
#include "material.h"
#include "sphere.h"
#include "triangle.h"
#include "mesh.h"
#include "texture.h"

int main() {
    hittable_list world;

    // Ground
    auto ground_material = make_shared<lambertian>(make_shared<checker_texture>(0.32, color(.2, .3, .1), color(.9, .9, .9)));
    world.add(make_shared<sphere>(point3(0,-1000,0), 1000, ground_material));

    // Individual triangles
    auto red_material = make_shared<lambertian>(color(0.7, 0.3, 0.3));
    auto green_material = make_shared<lambertian>(color(0.3, 0.7, 0.3));
    auto blue_material = make_shared<lambertian>(color(0.3, 0.3, 0.7));

    // Create a pyramid using individual triangles
    point3 apex(0, 2, 0);
    point3 base1(-1, 0, -1);
    point3 base2(1, 0, -1);
    point3 base3(1, 0, 1);
    point3 base4(-1, 0, 1);

    world.add(make_shared<triangle>(apex, base1, base2, red_material));
    world.add(make_shared<triangle>(apex, base2, base3, green_material));
    world.add(make_shared<triangle>(apex, base3, base4, blue_material));
    world.add(make_shared<triangle>(apex, base4, base1, red_material));

    // Cube meshes
    auto metal_material = make_shared<metal>(color(0.8, 0.8, 0.9), 0.0);
    auto cube1 = create_cube_mesh(metal_material, vec3(1, 1, 1), vec3(-3, 1, 0));
    world.add(cube1);

    auto glass_material = make_shared<dielectric>(1.5);
    auto cube2 = create_cube_mesh(glass_material, vec3(0.8, 0.8, 0.8), vec3(3, 1, 0));
    world.add(cube2);

    // Light
    auto light = make_shared<diffuse_light>(color(4, 4, 4));
    world.add(make_shared<sphere>(point3(0, 7, 0), 1, light));

    camera cam;

    cam.aspect_ratio      = 16.0 / 9.0;
    cam.image_width       = 400;
    cam.samples_per_pixel = 100;
    cam.max_depth         = 50;
    cam.background        = color(0.1, 0.1, 0.1);

    cam.vfov     = 40;
    cam.lookfrom = point3(0, 4, 8);
    cam.lookat   = point3(0, 1, 0);
    cam.vup      = vec3(0,1,0);

    cam.defocus_angle = 0;

    cam.render(world);
}
