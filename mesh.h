#ifndef MESH_H
#define MESH_H

#include "rtweekend.h"
#include "hittable.h"
#include "hittable_list.h"
#include "triangle.h"
#include "bvh.h"
#include <fstream>
#include <sstream>
#include <string>

class mesh : public hittable {
public:
    mesh(const std::string& filename, shared_ptr<material> mat, const vec3& scale = vec3(1,1,1), const vec3& offset = vec3(0,0,0)) {
        load_obj(filename, mat, scale, offset);
        if (!triangles.objects.empty()) {
            bvh_root = make_shared<bvh_node>(triangles);
        }
    }

    bool hit(const ray& r, interval ray_t, hit_record& rec) const override {
        if (bvh_root) {
            return bvh_root->hit(r, ray_t, rec);
        }
        return triangles.hit(r, ray_t, rec);
    }

    aabb bounding_box() const override {
        if (bvh_root) {
            return bvh_root->bounding_box();
        }
        return triangles.bounding_box();
    }

private:
    hittable_list triangles;
    shared_ptr<bvh_node> bvh_root;

    void load_obj(const std::string& filename, shared_ptr<material> mat, const vec3& scale, const vec3& offset) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::clog << "Warning: Could not open OBJ file: " << filename << std::endl;
            return;
        }

        std::vector<point3> vertices;
        std::string line;

        while (std::getline(file, line)) {
            std::istringstream iss(line);
            std::string prefix;
            iss >> prefix;

            if (prefix == "v") {
                // Vertex
                double x, y, z;
                iss >> x >> y >> z;
                // Apply scale and offset
                vertices.push_back(point3(x * scale.x() + offset.x(), 
                                         y * scale.y() + offset.y(), 
                                         z * scale.z() + offset.z()));
            } else if (prefix == "f") {
                // Face (assuming triangular faces)
                std::string vertex1, vertex2, vertex3;
                iss >> vertex1 >> vertex2 >> vertex3;

                // Parse vertex indices (OBJ uses 1-based indexing)
                int v1 = parse_vertex_index(vertex1) - 1;
                int v2 = parse_vertex_index(vertex2) - 1;
                int v3 = parse_vertex_index(vertex3) - 1;

                // Check bounds
                if (v1 >= 0 && v1 < vertices.size() &&
                    v2 >= 0 && v2 < vertices.size() &&
                    v3 >= 0 && v3 < vertices.size()) {
                    
                    triangles.add(make_shared<triangle>(vertices[v1], vertices[v2], vertices[v3], mat));
                }
            }
            // Ignore other OBJ elements for simplicity
        }

        file.close();
        std::clog << "Loaded mesh with " << triangles.objects.size() << " triangles from " << filename << std::endl;
    }

    int parse_vertex_index(const std::string& vertex_str) {
        // Simple parser for vertex indices (ignores texture and normal indices)
        size_t slash_pos = vertex_str.find('/');
        if (slash_pos != std::string::npos) {
            return std::stoi(vertex_str.substr(0, slash_pos));
        }
        return std::stoi(vertex_str);
    }
};

// Utility function to create a simple cube mesh programmatically
inline shared_ptr<mesh> create_cube_mesh(shared_ptr<material> mat, const vec3& scale = vec3(1,1,1), const vec3& offset = vec3(0,0,0)) {
    // Create a temporary OBJ file for a unit cube
    std::string temp_filename = "temp_cube.obj";
    std::ofstream file(temp_filename);
    
    if (file.is_open()) {
        // Write cube vertices
        file << "v -0.5 -0.5 -0.5\n";
        file << "v  0.5 -0.5 -0.5\n";
        file << "v  0.5  0.5 -0.5\n";
        file << "v -0.5  0.5 -0.5\n";
        file << "v -0.5 -0.5  0.5\n";
        file << "v  0.5 -0.5  0.5\n";
        file << "v  0.5  0.5  0.5\n";
        file << "v -0.5  0.5  0.5\n";
        
        // Write cube faces (triangles)
        file << "f 1 2 3\nf 1 3 4\n";  // front
        file << "f 2 6 7\nf 2 7 3\n";  // right
        file << "f 6 5 8\nf 6 8 7\n";  // back
        file << "f 5 1 4\nf 5 4 8\n";  // left
        file << "f 4 3 7\nf 4 7 8\n";  // top
        file << "f 5 6 2\nf 5 2 1\n";  // bottom
        
        file.close();
    }
    
    auto cube = make_shared<mesh>(temp_filename, mat, scale, offset);
    
    // Clean up temporary file
    std::remove(temp_filename.c_str());
    
    return cube;
}

#endif
