# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: RayTracer
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Documents/augment-projects/Raytracer/build/
# =============================================================================
# Object build statements for EXECUTABLE target raytracer


#############################################
# Order-only phony target for raytracer

build cmake_object_order_depends_target_raytracer: phony || .

build CMakeFiles/raytracer.dir/main.cpp.o: CXX_COMPILER__raytracer_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/main.cpp || cmake_object_order_depends_target_raytracer
  CONFIG = Debug
  DEP_FILE = CMakeFiles/raytracer.dir/main.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/raytracer.dir
  OBJECT_FILE_DIR = CMakeFiles/raytracer.dir


# =============================================================================
# Link build statements for EXECUTABLE target raytracer


#############################################
# Link the executable raytracer

build raytracer: CXX_EXECUTABLE_LINKER__raytracer_Debug CMakeFiles/raytracer.dir/main.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/raytracer.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = raytracer
  TARGET_PDB = raytracer.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_scene


#############################################
# Order-only phony target for test_scene

build cmake_object_order_depends_target_test_scene: phony || .

build CMakeFiles/test_scene.dir/test_scene.cpp.o: CXX_COMPILER__test_scene_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp || cmake_object_order_depends_target_test_scene
  CONFIG = Debug
  DEP_FILE = CMakeFiles/test_scene.dir/test_scene.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/test_scene.dir
  OBJECT_FILE_DIR = CMakeFiles/test_scene.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_scene


#############################################
# Link the executable test_scene

build test_scene: CXX_EXECUTABLE_LINKER__test_scene_Debug CMakeFiles/test_scene.dir/test_scene.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/test_scene.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test_scene
  TARGET_PDB = test_scene.dbg

# =============================================================================
# Object build statements for EXECUTABLE target cornell_box


#############################################
# Order-only phony target for cornell_box

build cmake_object_order_depends_target_cornell_box: phony || .

build CMakeFiles/cornell_box.dir/cornell_box.cpp.o: CXX_COMPILER__cornell_box_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/cornell_box.cpp || cmake_object_order_depends_target_cornell_box
  CONFIG = Debug
  DEP_FILE = CMakeFiles/cornell_box.dir/cornell_box.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/cornell_box.dir
  OBJECT_FILE_DIR = CMakeFiles/cornell_box.dir


# =============================================================================
# Link build statements for EXECUTABLE target cornell_box


#############################################
# Link the executable cornell_box

build cornell_box: CXX_EXECUTABLE_LINKER__cornell_box_Debug CMakeFiles/cornell_box.dir/cornell_box.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/cornell_box.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = cornell_box
  TARGET_PDB = cornell_box.dbg

# =============================================================================
# Object build statements for EXECUTABLE target motion_blur


#############################################
# Order-only phony target for motion_blur

build cmake_object_order_depends_target_motion_blur: phony || .

build CMakeFiles/motion_blur.dir/motion_blur.cpp.o: CXX_COMPILER__motion_blur_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/motion_blur.cpp || cmake_object_order_depends_target_motion_blur
  CONFIG = Debug
  DEP_FILE = CMakeFiles/motion_blur.dir/motion_blur.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/motion_blur.dir
  OBJECT_FILE_DIR = CMakeFiles/motion_blur.dir


# =============================================================================
# Link build statements for EXECUTABLE target motion_blur


#############################################
# Link the executable motion_blur

build motion_blur: CXX_EXECUTABLE_LINKER__motion_blur_Debug CMakeFiles/motion_blur.dir/motion_blur.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/motion_blur.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = motion_blur
  TARGET_PDB = motion_blur.dbg

# =============================================================================
# Object build statements for EXECUTABLE target textures


#############################################
# Order-only phony target for textures

build cmake_object_order_depends_target_textures: phony || .

build CMakeFiles/textures.dir/textures.cpp.o: CXX_COMPILER__textures_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/textures.cpp || cmake_object_order_depends_target_textures
  CONFIG = Debug
  DEP_FILE = CMakeFiles/textures.dir/textures.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/textures.dir
  OBJECT_FILE_DIR = CMakeFiles/textures.dir


# =============================================================================
# Link build statements for EXECUTABLE target textures


#############################################
# Link the executable textures

build textures: CXX_EXECUTABLE_LINKER__textures_Debug CMakeFiles/textures.dir/textures.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/textures.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = textures
  TARGET_PDB = textures.dbg

# =============================================================================
# Object build statements for EXECUTABLE target volumes


#############################################
# Order-only phony target for volumes

build cmake_object_order_depends_target_volumes: phony || .

build CMakeFiles/volumes.dir/volumes.cpp.o: CXX_COMPILER__volumes_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/volumes.cpp || cmake_object_order_depends_target_volumes
  CONFIG = Debug
  DEP_FILE = CMakeFiles/volumes.dir/volumes.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/volumes.dir
  OBJECT_FILE_DIR = CMakeFiles/volumes.dir


# =============================================================================
# Link build statements for EXECUTABLE target volumes


#############################################
# Link the executable volumes

build volumes: CXX_EXECUTABLE_LINKER__volumes_Debug CMakeFiles/volumes.dir/volumes.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/volumes.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = volumes
  TARGET_PDB = volumes.dbg

# =============================================================================
# Object build statements for EXECUTABLE target triangles


#############################################
# Order-only phony target for triangles

build cmake_object_order_depends_target_triangles: phony || .

build CMakeFiles/triangles.dir/triangles.cpp.o: CXX_COMPILER__triangles_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/triangles.cpp || cmake_object_order_depends_target_triangles
  CONFIG = Debug
  DEP_FILE = CMakeFiles/triangles.dir/triangles.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/triangles.dir
  OBJECT_FILE_DIR = CMakeFiles/triangles.dir


# =============================================================================
# Link build statements for EXECUTABLE target triangles


#############################################
# Link the executable triangles

build triangles: CXX_EXECUTABLE_LINKER__triangles_Debug CMakeFiles/triangles.dir/triangles.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/triangles.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = triangles
  TARGET_PDB = triangles.dbg

# =============================================================================
# Object build statements for EXECUTABLE target final_scene


#############################################
# Order-only phony target for final_scene

build cmake_object_order_depends_target_final_scene: phony || .

build CMakeFiles/final_scene.dir/final_scene.cpp.o: CXX_COMPILER__final_scene_unscanned_Debug /Users/<USER>/Documents/augment-projects/Raytracer/final_scene.cpp || cmake_object_order_depends_target_final_scene
  CONFIG = Debug
  DEP_FILE = CMakeFiles/final_scene.dir/final_scene.cpp.o.d
  FLAGS = -Wall -Wextra -O3 -g -std=gnu++17
  OBJECT_DIR = CMakeFiles/final_scene.dir
  OBJECT_FILE_DIR = CMakeFiles/final_scene.dir


# =============================================================================
# Link build statements for EXECUTABLE target final_scene


#############################################
# Link the executable final_scene

build final_scene: CXX_EXECUTABLE_LINKER__final_scene_Debug CMakeFiles/final_scene.dir/final_scene.cpp.o
  CONFIG = Debug
  FLAGS = -Wall -Wextra -O3 -g
  OBJECT_DIR = CMakeFiles/final_scene.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = final_scene
  TARGET_PDB = final_scene.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/augment-projects/Raytracer/build && /usr/local/bin/ccmake -S/Users/<USER>/Documents/augment-projects/Raytracer -B/Users/<USER>/Documents/augment-projects/Raytracer/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/augment-projects/Raytracer/build && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/augment-projects/Raytracer -B/Users/<USER>/Documents/augment-projects/Raytracer/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/augment-projects/Raytracer/build

build all: phony raytracer test_scene cornell_box motion_blur textures volumes triangles final_scene

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /Users/<USER>/Documents/augment-projects/Raytracer/build/cmake_install.cmake: RERUN_CMAKE | /Users/<USER>/Documents/augment-projects/Raytracer/CMakeLists.txt /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCXXInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeCXXCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Documents/augment-projects/Raytracer/CMakeLists.txt /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCXXInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeCXXCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
