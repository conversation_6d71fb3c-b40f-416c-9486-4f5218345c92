{"artifacts": [{"path": "cornell_box"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -O3 -g -std=gnu++17"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "id": "cornell_box::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -O3 -g", "role": "flags"}], "language": "CXX"}, "name": "cornell_box", "nameOnDisk": "cornell_box", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "cornell_box.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}