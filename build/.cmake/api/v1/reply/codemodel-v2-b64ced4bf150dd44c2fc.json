{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "RayTracer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "cornell_box::@6890427a1f51a3e7e1df", "jsonFile": "target-cornell_box-Debug-1f12dec17144781a9168.json", "name": "cornell_box", "projectIndex": 0}, {"directoryIndex": 0, "id": "final_scene::@6890427a1f51a3e7e1df", "jsonFile": "target-final_scene-Debug-789d6421b76770557847.json", "name": "final_scene", "projectIndex": 0}, {"directoryIndex": 0, "id": "motion_blur::@6890427a1f51a3e7e1df", "jsonFile": "target-motion_blur-Debug-b5d7f207f00f156a9091.json", "name": "motion_blur", "projectIndex": 0}, {"directoryIndex": 0, "id": "raytracer::@6890427a1f51a3e7e1df", "jsonFile": "target-raytracer-Debug-32d9727376c375fb71b3.json", "name": "raytracer", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_scene::@6890427a1f51a3e7e1df", "jsonFile": "target-test_scene-Debug-377f0548af332275b34b.json", "name": "test_scene", "projectIndex": 0}, {"directoryIndex": 0, "id": "textures::@6890427a1f51a3e7e1df", "jsonFile": "target-textures-Debug-0ca4ce2d045eeae57024.json", "name": "textures", "projectIndex": 0}, {"directoryIndex": 0, "id": "triangles::@6890427a1f51a3e7e1df", "jsonFile": "target-triangles-Debug-715ebcb5994cd05c1bac.json", "name": "triangles", "projectIndex": 0}, {"directoryIndex": 0, "id": "volumes::@6890427a1f51a3e7e1df", "jsonFile": "target-volumes-Debug-1d2d60265ed938d9a2b6.json", "name": "volumes", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "source": "/Users/<USER>/Documents/augment-projects/Raytracer"}, "version": {"major": 2, "minor": 8}}