{"kind": "toolchains", "toolchains": [{"compiler": {"id": "AppleClang", "implicit": {"includeDirectories": ["/usr/local/include", "/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include", "/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include", "/Library/Developer/CommandLineTools/usr/include"], "linkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/lib"], "linkFrameworkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/System/Library/Frameworks"], "linkLibraries": []}, "path": "/usr/bin/clang", "version": "12.0.0.12000032"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "AppleClang", "implicit": {"includeDirectories": ["/usr/local/include", "/Library/Developer/CommandLineTools/usr/include/c++/v1", "/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include", "/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include", "/Library/Developer/CommandLineTools/usr/include"], "linkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/lib"], "linkFrameworkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/System/Library/Frameworks"], "linkLibraries": ["c++"]}, "path": "/usr/bin/clang++", "version": "12.0.0.12000032"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}