{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "source": "/Users/<USER>/Documents/augment-projects/Raytracer"}, "version": {"major": 1, "minor": 1}}