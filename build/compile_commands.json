[{"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -<PERSON> -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/raytracer.dir/main.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/main.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/main.cpp", "output": "CMakeFiles/raytracer.dir/main.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/test_scene.dir/test_scene.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp", "output": "CMakeFiles/test_scene.dir/test_scene.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/cornell_box.dir/cornell_box.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/cornell_box.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/cornell_box.cpp", "output": "CMakeFiles/cornell_box.dir/cornell_box.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/motion_blur.dir/motion_blur.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/motion_blur.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/motion_blur.cpp", "output": "CMakeFiles/motion_blur.dir/motion_blur.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/textures.dir/textures.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/textures.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/textures.cpp", "output": "CMakeFiles/textures.dir/textures.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/volumes.dir/volumes.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/volumes.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/volumes.cpp", "output": "CMakeFiles/volumes.dir/volumes.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/triangles.dir/triangles.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/triangles.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/triangles.cpp", "output": "CMakeFiles/triangles.dir/triangles.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/Raytracer/build", "command": "/usr/bin/clang++  -Wall -Wextra -O3 -g -std=gnu++17 -o CMakeFiles/final_scene.dir/final_scene.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/final_scene.cpp", "file": "/Users/<USER>/Documents/augment-projects/Raytracer/final_scene.cpp", "output": "CMakeFiles/final_scene.dir/final_scene.cpp.o"}]