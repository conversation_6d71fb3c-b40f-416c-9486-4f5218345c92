# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

CMakeFiles/test_scene.dir/test_scene.cpp.o: /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/Availability.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/AvailabilityInternal.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/AvailabilityVersions.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/__wctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_ctermid.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_ctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_locale.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_stdio.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_intmax_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_nl_item.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint16_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint32_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint64_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint8_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uintmax_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_wctrans_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_wctype_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_wctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_xlocale.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/alloca.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/assert.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/ctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/errno.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/_limits.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/_mcontext.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/_types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/endian.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/limits.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/signal.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/libkern/_OSByteOrder.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/libkern/i386/_OSByteOrder.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/limits.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/locale.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/mach/i386/_structs.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/mach/machine/_structs.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/_mcontext.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/_types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/endian.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/limits.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/signal.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/malloc/_malloc.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/math.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/nl_types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread/pthread_impl.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread/qos.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread/sched.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/runetype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sched.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/stdint.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/stdio.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/stdlib.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/string.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/strings.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_endian.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_posix_availability.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_attr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_cond_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_condattr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_key_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_mutex_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_mutexattr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_once_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_rwlock_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_rwlockattr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_symbol_aliasing.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_blkcnt_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_blksize_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_caddr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_clock_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ct_rune_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_dev_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_errno_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_clr.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_copy.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_def.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_isset.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_set.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_setsize.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_zero.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fsblkcnt_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fsfilcnt_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_gid_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_id_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_in_addr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_in_port_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ino64_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ino_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int16_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int32_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int64_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int8_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_intptr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_key_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_mach_port_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_mbstate_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_mode_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_nlink_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_null.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_off_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_pid_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_rsize_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_rune_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_sigaltstack.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_sigset_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_size_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ssize_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_suseconds_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_time_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_timespec.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_timeval.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_char.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int16_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int32_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int64_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int8_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_short.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ucontext.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_uid_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_uintptr_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_useconds_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_va_list.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_wchar_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_wint_t.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/appleapiopts.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/cdefs.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/errno.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/qos.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/resource.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/signal.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/stdio.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/syslimits.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/types.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/wait.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/time.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/wchar.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/wctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/__wctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_ctype.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_stdio.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_stdlib.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_string.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_time.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_wchar.h \
  /Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_wctype.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__bit_reference \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__bsd_locale_defaults.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__config \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__cxx_version \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__debug \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__errc \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__functional_base \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__locale \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__mutex_base \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__nullptr \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__split_buffer \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__string \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__threading_support \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__tuple \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/__undef_macros \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/algorithm \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/atomic \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/bit \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/bitset \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cassert \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cctype \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cerrno \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/chrono \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/climits \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cmath \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cstddef \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cstdint \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cstdio \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cstdlib \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cstring \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/ctime \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/ctype.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cwchar \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/cwctype \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/errno.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/exception \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/functional \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/initializer_list \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/ios \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/iosfwd \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/iostream \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/istream \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/iterator \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/limits \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/limits.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/locale \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/locale.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/math.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/memory \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/mutex \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/new \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/numeric \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/ostream \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/random \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/ratio \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/stddef.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/stdexcept \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/stdint.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/stdio.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/stdlib.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/streambuf \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/string \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/string.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/string_view \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/system_error \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/tuple \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/type_traits \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/typeinfo \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/utility \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/vector \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/wchar.h \
  /Library/Developer/CommandLineTools/usr/include/c++/v1/wctype.h \
  /Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/__stddef_max_align_t.h \
  /Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/limits.h \
  /Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/stdarg.h \
  /Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/stddef.h \
  /Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/stdint.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/camera.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/color.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/hittable.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/hittable_list.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/material.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/ray.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/rtweekend.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/sphere.h \
  /Users/<USER>/Documents/augment-projects/Raytracer/vec3.h


/Users/<USER>/Documents/augment-projects/Raytracer/vec3.h:

/Users/<USER>/Documents/augment-projects/Raytracer/rtweekend.h:

/Users/<USER>/Documents/augment-projects/Raytracer/material.h:

/Users/<USER>/Documents/augment-projects/Raytracer/hittable_list.h:

/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/stdint.h:

/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/limits.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/wctype.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/wchar.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/vector:

/Library/Developer/CommandLineTools/usr/include/c++/v1/utility:

/Library/Developer/CommandLineTools/usr/include/c++/v1/typeinfo:

/Library/Developer/CommandLineTools/usr/include/c++/v1/string_view:

/Library/Developer/CommandLineTools/usr/include/c++/v1/string:

/Library/Developer/CommandLineTools/usr/include/c++/v1/streambuf:

/Library/Developer/CommandLineTools/usr/include/c++/v1/stdlib.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/stdio.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/stdint.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/random:

/Library/Developer/CommandLineTools/usr/include/c++/v1/ostream:

/Library/Developer/CommandLineTools/usr/include/c++/v1/mutex:

/Library/Developer/CommandLineTools/usr/include/c++/v1/math.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/locale.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/locale:

/Library/Developer/CommandLineTools/usr/include/c++/v1/limits.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/string.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/initializer_list:

/Library/Developer/CommandLineTools/usr/include/c++/v1/system_error:

/Library/Developer/CommandLineTools/usr/include/c++/v1/functional:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uintmax_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/exception:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__tuple:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cstring:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cstdint:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cstddef:

/Library/Developer/CommandLineTools/usr/include/c++/v1/climits:

/Library/Developer/CommandLineTools/usr/include/c++/v1/chrono:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cctype:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/limits.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/bitset:

/Library/Developer/CommandLineTools/usr/include/c++/v1/atomic:

/Library/Developer/CommandLineTools/usr/include/c++/v1/numeric:

/Library/Developer/CommandLineTools/usr/include/c++/v1/algorithm:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__undef_macros:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__string:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__nullptr:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__mutex_base:

/Library/Developer/CommandLineTools/usr/include/c++/v1/tuple:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__locale:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__cxx_version:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__bit_reference:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_wctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_clock_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cstdio:

/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/__stddef_max_align_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_wchar.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/ctime:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_string.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/iostream:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_sigset_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_stdlib.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__config:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_stdio.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_ctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/__wctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/wctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_errno_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale/_time.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/stddef.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_ctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/time.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/wait.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_xlocale.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/istream:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/types.h:

/Users/<USER>/Documents/augment-projects/Raytracer/sphere.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/syslimits.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/qos.h:

/Users/<USER>/Documents/augment-projects/Raytracer/color.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/errno.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ucontext.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__functional_base:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/cdefs.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_wchar_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_key_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/xlocale.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_uintptr_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_uid_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cassert:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int64_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_short.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int32_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/_mcontext.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cerrno:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_off_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/stdexcept:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_char.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_timespec.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ssize_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/wchar.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_time_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_size_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/memory:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_mutex_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/bit:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__errc:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_sigaltstack.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_rune_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/new:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/stdint.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_null.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/nl_types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_nlink_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/ratio:

/Library/Developer/CommandLineTools/usr/include/c++/v1/errno.h:

/Users/<USER>/Documents/augment-projects/Raytracer/hittable.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_mbstate_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_intptr_t.h:

/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/stdarg.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/limits:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int8_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_rsize_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int64_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_va_list.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_wctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int32_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/strings.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/endian.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/__wctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_int16_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_wctrans_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/resource.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/_mcontext.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_gid_t.h:

/Library/Developer/CommandLineTools/usr/lib/clang/12.0.0/include/stddef.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fsfilcnt_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cstdlib:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_setsize.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_once_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/signal.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ino64_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_copy.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/string.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ct_rune_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_suseconds_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/_types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_timeval.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/runetype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_key_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/malloc/_malloc.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_symbol_aliasing.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/locale.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_rwlockattr_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/appleapiopts.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__debug:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_condattr_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_dev_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/iterator:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_posix_availability.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/limits.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/libkern/_OSByteOrder.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_mutexattr_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/stdlib.h:

/Users/<USER>/Documents/augment-projects/Raytracer/camera.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/stdio.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cwctype:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sched.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread/sched.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread/pthread_impl.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_mode_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/mach/i386/_structs.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/pthread/qos.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/libkern/i386/_OSByteOrder.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int8_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_mach_port_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_u_int16_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_cond_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_ino_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_attr_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_in_addr_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/alloca.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_isset.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/_types.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__bsd_locale_defaults.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/mach/machine/_structs.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint8_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/stdio.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/limits.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_in_port_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_id_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__split_buffer:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_rwlock_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/signal.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/type_traits:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/types.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/signal.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/AvailabilityInternal.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/math.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_clr.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/i386/_limits.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/ios:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint64_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_wctype_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_ctermid.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/ctype.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cwchar:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/Availability.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_nl_item.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_wint_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/assert.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_zero.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_caddr_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/__threading_support:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_def.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_pid_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint16_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fsblkcnt_t.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/iosfwd:

/Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp:

/Library/Developer/CommandLineTools/usr/include/c++/v1/cmath:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_useconds_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_pthread/_pthread_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_blkcnt_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_intmax_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_stdio.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/errno.h:

/Library/Developer/CommandLineTools/usr/include/c++/v1/ctype.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_locale.h:

/Users/<USER>/Documents/augment-projects/Raytracer/ray.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_blksize_t.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/machine/endian.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/AvailabilityVersions.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_endian.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/sys/_types/_fd_set.h:

/Library/Developer/CommandLineTools/SDKs/MacOSX11.0.sdk/usr/include/_types/_uint32_t.h:
