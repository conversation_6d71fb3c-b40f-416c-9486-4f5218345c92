# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/Raytracer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/Raytracer/build

# Include any dependencies generated for this target.
include CMakeFiles/test_scene.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_scene.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_scene.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_scene.dir/flags.make

CMakeFiles/test_scene.dir/codegen:
.PHONY : CMakeFiles/test_scene.dir/codegen

CMakeFiles/test_scene.dir/test_scene.cpp.o: CMakeFiles/test_scene.dir/flags.make
CMakeFiles/test_scene.dir/test_scene.cpp.o: /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp
CMakeFiles/test_scene.dir/test_scene.cpp.o: CMakeFiles/test_scene.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_scene.dir/test_scene.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_scene.dir/test_scene.cpp.o -MF CMakeFiles/test_scene.dir/test_scene.cpp.o.d -o CMakeFiles/test_scene.dir/test_scene.cpp.o -c /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp

CMakeFiles/test_scene.dir/test_scene.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_scene.dir/test_scene.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp > CMakeFiles/test_scene.dir/test_scene.cpp.i

CMakeFiles/test_scene.dir/test_scene.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_scene.dir/test_scene.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/Raytracer/test_scene.cpp -o CMakeFiles/test_scene.dir/test_scene.cpp.s

# Object files for target test_scene
test_scene_OBJECTS = \
"CMakeFiles/test_scene.dir/test_scene.cpp.o"

# External object files for target test_scene
test_scene_EXTERNAL_OBJECTS =

test_scene: CMakeFiles/test_scene.dir/test_scene.cpp.o
test_scene: CMakeFiles/test_scene.dir/build.make
test_scene: CMakeFiles/test_scene.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable test_scene"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_scene.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_scene.dir/build: test_scene
.PHONY : CMakeFiles/test_scene.dir/build

CMakeFiles/test_scene.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_scene.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_scene.dir/clean

CMakeFiles/test_scene.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/Raytracer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/Raytracer /Users/<USER>/Documents/augment-projects/Raytracer /Users/<USER>/Documents/augment-projects/Raytracer/build /Users/<USER>/Documents/augment-projects/Raytracer/build /Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles/test_scene.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_scene.dir/depend

