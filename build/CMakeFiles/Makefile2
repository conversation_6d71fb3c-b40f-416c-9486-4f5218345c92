# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/Raytracer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/Raytracer/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/raytracer.dir/all
all: CMakeFiles/test_scene.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/raytracer.dir/codegen
codegen: CMakeFiles/test_scene.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/raytracer.dir/clean
clean: CMakeFiles/test_scene.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/raytracer.dir

# All Build rule for target.
CMakeFiles/raytracer.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/raytracer.dir/build.make CMakeFiles/raytracer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/raytracer.dir/build.make CMakeFiles/raytracer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles --progress-num=1,2 "Built target raytracer"
.PHONY : CMakeFiles/raytracer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/raytracer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/raytracer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles 0
.PHONY : CMakeFiles/raytracer.dir/rule

# Convenience name for target.
raytracer: CMakeFiles/raytracer.dir/rule
.PHONY : raytracer

# codegen rule for target.
CMakeFiles/raytracer.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/raytracer.dir/build.make CMakeFiles/raytracer.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles --progress-num=1,2 "Finished codegen for target raytracer"
.PHONY : CMakeFiles/raytracer.dir/codegen

# clean rule for target.
CMakeFiles/raytracer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/raytracer.dir/build.make CMakeFiles/raytracer.dir/clean
.PHONY : CMakeFiles/raytracer.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_scene.dir

# All Build rule for target.
CMakeFiles/test_scene.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_scene.dir/build.make CMakeFiles/test_scene.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_scene.dir/build.make CMakeFiles/test_scene.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles --progress-num=3,4 "Built target test_scene"
.PHONY : CMakeFiles/test_scene.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_scene.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_scene.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles 0
.PHONY : CMakeFiles/test_scene.dir/rule

# Convenience name for target.
test_scene: CMakeFiles/test_scene.dir/rule
.PHONY : test_scene

# codegen rule for target.
CMakeFiles/test_scene.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_scene.dir/build.make CMakeFiles/test_scene.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/augment-projects/Raytracer/build/CMakeFiles --progress-num=3,4 "Finished codegen for target test_scene"
.PHONY : CMakeFiles/test_scene.dir/codegen

# clean rule for target.
CMakeFiles/test_scene.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_scene.dir/build.make CMakeFiles/test_scene.dir/clean
.PHONY : CMakeFiles/test_scene.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

