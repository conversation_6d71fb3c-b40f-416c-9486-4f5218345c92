# Ray Tracer - "Ray Tracing in One Weekend"

This is a complete implementation of the ray tracer from <PERSON>'s book ["Ray Tracing in One Weekend"](https://raytracing.github.io/books/RayTracingInOneWeekend.html).

## Features

- **PPM Image Output**: Generates images in the simple PPM format
- **Antialiasing**: Multiple samples per pixel for smooth edges
- **Materials**:
  - Lambertian (diffuse) materials
  - Metal materials with controllable fuzziness
  - Dielectric (glass) materials with refraction
- **Camera Controls**:
  - Positionable camera with lookfrom/lookat
  - Adjustable field of view
  - Defocus blur (depth of field)
- **Progress Reporting**: Shows rendering progress in real-time

## Building

### Prerequisites
- C++17 compatible compiler (GCC, Clang, or MSVC)
- CMake 3.10 or higher

### Build Instructions

1. **Create build directory:**
   ```bash
   mkdir build
   cd build
   ```

2. **Configure with CMake:**
   ```bash
   cmake ..
   ```

3. **Build the project:**
   ```bash
   cmake --build .
   ```

   For optimized release build:
   ```bash
   cmake --build . --config Release
   ```

This will create two executables:
- `raytracer` - The main program with the full scene from the book
- `test_scene` - A simpler test scene for quick verification

## Running

After building, run the ray tracer and redirect output to a PPM file:

**For the full scene (takes several minutes):**
```bash
./raytracer > final_scene.ppm
```

**For a quick test (takes ~30 seconds):**
```bash
./test_scene > test_image.ppm
```

On Windows:
```bash
raytracer.exe > final_scene.ppm
test_scene.exe > test_image.ppm
```

## Viewing the Output

The ray tracer outputs images in PPM format. You can view these with:
- **macOS**: Preview, ToyViewer
- **Windows**: IrfanView, GIMP, Photoshop
- **Linux**: GIMP, ImageMagick (`display image.ppm`)
- **Online**: Search for "PPM viewer" for web-based viewers

## Configuration

You can modify the rendering parameters in `main.cpp`:

- `image_width`: Width of the output image (height calculated from aspect ratio)
- `samples_per_pixel`: Number of samples per pixel (higher = better quality, slower)
- `max_depth`: Maximum ray bounce depth
- `vfov`: Vertical field of view in degrees
- `lookfrom`, `lookat`: Camera position and target
- `defocus_angle`: Controls depth of field blur
- `focus_dist`: Distance to the focus plane

## Performance Notes

- The default settings use 10 samples per pixel for reasonable render times during development
- For high-quality final renders, increase `samples_per_pixel` to 100-500
- The final scene contains ~500 spheres and can take several minutes to render at high quality
- Use Release builds for significantly better performance

## Scene Description

The final scene includes:
- A large ground sphere
- 484 small randomly placed spheres with random materials
- Three large spheres showcasing different materials:
  - Glass sphere (dielectric)
  - Matte sphere (lambertian)
  - Metal sphere

## File Structure

- `main.cpp` - Main program and scene setup
- `rtweekend.h` - Common utilities and constants
- `vec3.h` - 3D vector mathematics
- `color.h` - Color utilities and PPM output
- `ray.h` - Ray class for ray-object intersection
- `hittable.h` - Abstract interface for ray-hittable objects
- `sphere.h` - Sphere implementation
- `hittable_list.h` - Collection of hittable objects
- `material.h` - Material classes (lambertian, metal, dielectric)
- `camera.h` - Camera class with all features

## Next Steps

This implementation covers the complete "Ray Tracing in One Weekend" book. To continue learning:

1. **Ray Tracing: The Next Week** - Adds motion blur, textures, volumes, and more
2. **Ray Tracing: The Rest of Your Life** - Advanced techniques for professional-quality rendering

## License

This implementation follows the structure and algorithms from the "Ray Tracing in One Weekend" series by Peter Shirley, Trevor David Black, and Steve Hollasch.
