#include "rtweekend.h"

#include "bvh.h"
#include "camera.h"
#include "color.h"
#include "hittable_list.h"
#include "material.h"
#include "sphere.h"
#include "texture.h"

int main() {
    hittable_list world;

    auto checker = make_shared<checker_texture>(0.32, color(.2, .3, .1), color(.9, .9, .9));
    world.add(make_shared<sphere>(point3(0,-10,0), 10, make_shared<lambertian>(checker)));

    auto pertext = make_shared<noise_texture>(4);
    world.add(make_shared<sphere>(point3(0,2,0), 2, make_shared<lambertian>(pertext)));

    auto red = make_shared<solid_color>(color(0.7, 0.3, 0.3));
    world.add(make_shared<sphere>(point3(-4,2,0), 2, make_shared<lambertian>(red)));

    auto blue = make_shared<solid_color>(color(0.3, 0.3, 0.7));
    world.add(make_shared<sphere>(point3(4,2,0), 2, make_shared<lambertian>(blue)));

    camera cam;

    cam.aspect_ratio      = 16.0 / 9.0;
    cam.image_width       = 400;
    cam.samples_per_pixel = 100;
    cam.max_depth         = 50;
    cam.background        = color(0.70, 0.80, 1.00);

    cam.vfov     = 20;
    cam.lookfrom = point3(13,2,3);
    cam.lookat   = point3(0,2,0);
    cam.vup      = vec3(0,1,0);

    cam.defocus_angle = 0;

    cam.render(world);
}
