#include "rtweekend.h"

#include "camera.h"
#include "color.h"
#include "hittable_list.h"
#include "material.h"
#include "sphere.h"

int main() {
    // Simple test scene with just a few spheres
    hittable_list world;

    auto ground_material = make_shared<lambertian>(color(0.8, 0.8, 0.0));
    auto center_material = make_shared<lambertian>(color(0.1, 0.2, 0.5));
    auto left_material   = make_shared<dielectric>(1.50);
    auto bubble_material = make_shared<dielectric>(1.00 / 1.50);
    auto right_material  = make_shared<metal>(color(0.8, 0.6, 0.2), 0.0);

    world.add(make_shared<sphere>(point3( 0.0, -100.5, -1.0), 100.0, ground_material));
    world.add(make_shared<sphere>(point3( 0.0,    0.0, -1.2),   0.5, center_material));
    world.add(make_shared<sphere>(point3(-1.0,    0.0, -1.0),   0.5, left_material));
    world.add(make_shared<sphere>(point3(-1.0,    0.0, -1.0),   0.4, bubble_material));
    world.add(make_shared<sphere>(point3( 1.0,    0.0, -1.0),   0.5, right_material));

    camera cam;

    cam.aspect_ratio      = 16.0 / 9.0;
    cam.image_width       = 400;
    cam.samples_per_pixel = 100;
    cam.max_depth         = 50;

    cam.vfov     = 20;
    cam.lookfrom = point3(-2,2,1);
    cam.lookat   = point3(0,0,-1);
    cam.vup      = vec3(0,1,0);

    cam.defocus_angle = 10.0;
    cam.focus_dist    = 3.4;

    cam.render(world);
}
